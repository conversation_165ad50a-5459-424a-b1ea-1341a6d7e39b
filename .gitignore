# Dossiers Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Fichiers de build Tailwind
# Ne pas ignorer le fichier CSS final car il est nécessaire pour le site
# css/tailwind.css

# <PERSON><PERSON><PERSON> b<PERSON><PERSON> (site original)
b<PERSON><PERSON>/

# Dossier image pour que ce soit moins lourd
image/

# Fichiers Microsoft Office
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx

# Fichiers de cache et temporaires
.cache/
.temp/
.tmp/
dist/
build/

# Fichiers de configuration d'environnement
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Fichiers système
.DS_Store
Thumbs.db
desktop.ini

# Fichiers d'éditeur
.idea/
.vscode/
*.swp
*.swo
*~

# Fichiers de log
logs/
*.log

# Autres fichiers à ignorer
package-lock.json

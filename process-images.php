<?php
/**
 * Script pour traiter automatiquement les images du diaporama
 * Supprime les bandes blanches et optimise pour le responsive
 */

// Configuration
$source_folder = 'diaporama/';
$processed_folder = 'diaporama-processed/';
$backup_folder = 'diaporama-backup/';

// Extensions supportées
$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

echo "<h1>Traitement automatique des images</h1>";
echo "<p>Suppression des bandes blanches et optimisation responsive</p>";

// Créer les dossiers si nécessaire
if (!is_dir($processed_folder)) {
    mkdir($processed_folder, 0755, true);
    echo "<p style='color: green;'>✓ Dossier '$processed_folder' créé</p>";
}

if (!is_dir($backup_folder)) {
    mkdir($backup_folder, 0755, true);
    echo "<p style='color: green;'>✓ Dossier '$backup_folder' créé</p>";
}

// Fonction pour détecter et supprimer les bandes blanches
function removeWhiteBorders($source_path, $dest_path) {
    // Vérifier que GD est disponible
    if (!extension_loaded('gd')) {
        return "Extension GD non disponible";
    }
    
    // Obtenir les informations de l'image
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return "Impossible de lire l'image";
    }
    
    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];
    
    // Créer l'image source
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($source_path);
            break;
        default:
            return "Type d'image non supporté";
    }
    
    if (!$source) {
        return "Impossible de créer l'image source";
    }
    
    // Détecter les bordures blanches
    $top_border = 0;
    $bottom_border = 0;
    $left_border = 0;
    $right_border = 0;
    
    // Détecter bordure du haut
    for ($y = 0; $y < $height; $y++) {
        $is_white_row = true;
        for ($x = 0; $x < $width; $x++) {
            $rgb = imagecolorat($source, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;
            
            // Considérer comme blanc si très proche du blanc
            if ($r < 240 || $g < 240 || $b < 240) {
                $is_white_row = false;
                break;
            }
        }
        if ($is_white_row) {
            $top_border++;
        } else {
            break;
        }
    }
    
    // Détecter bordure du bas
    for ($y = $height - 1; $y >= 0; $y--) {
        $is_white_row = true;
        for ($x = 0; $x < $width; $x++) {
            $rgb = imagecolorat($source, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;
            
            if ($r < 240 || $g < 240 || $b < 240) {
                $is_white_row = false;
                break;
            }
        }
        if ($is_white_row) {
            $bottom_border++;
        } else {
            break;
        }
    }
    
    // Détecter bordure de gauche
    for ($x = 0; $x < $width; $x++) {
        $is_white_col = true;
        for ($y = 0; $y < $height; $y++) {
            $rgb = imagecolorat($source, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;
            
            if ($r < 240 || $g < 240 || $b < 240) {
                $is_white_col = false;
                break;
            }
        }
        if ($is_white_col) {
            $left_border++;
        } else {
            break;
        }
    }
    
    // Détecter bordure de droite
    for ($x = $width - 1; $x >= 0; $x--) {
        $is_white_col = true;
        for ($y = 0; $y < $height; $y++) {
            $rgb = imagecolorat($source, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;
            
            if ($r < 240 || $g < 240 || $b < 240) {
                $is_white_col = false;
                break;
            }
        }
        if ($is_white_col) {
            $right_border++;
        } else {
            break;
        }
    }
    
    // Calculer les nouvelles dimensions
    $new_width = $width - $left_border - $right_border;
    $new_height = $height - $top_border - $bottom_border;
    
    if ($new_width <= 0 || $new_height <= 0) {
        imagedestroy($source);
        return "Image entièrement blanche";
    }
    
    // Créer la nouvelle image
    $dest = imagecreatetruecolor($new_width, $new_height);
    
    // Préserver la transparence pour PNG
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($dest, false);
        imagesavealpha($dest, true);
    }
    
    // Copier la partie sans bordures
    imagecopy($dest, $source, 0, 0, $left_border, $top_border, $new_width, $new_height);
    
    // Sauvegarder
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($dest, $dest_path, 90);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($dest, $dest_path);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($dest, $dest_path);
            break;
    }
    
    // Nettoyer
    imagedestroy($source);
    imagedestroy($dest);
    
    if ($result) {
        return "Succès - Bordures supprimées: T:$top_border, B:$bottom_border, L:$left_border, R:$right_border";
    } else {
        return "Erreur lors de la sauvegarde";
    }
}

// Traiter les images
if (is_dir($source_folder)) {
    $files = scandir($source_folder);
    $processed_count = 0;
    
    echo "<h2>Traitement des images:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Fichier</th><th>Taille originale</th><th>Résultat</th><th>Nouvelle taille</th></tr>";
    
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $file_extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            
            if (in_array($file_extension, $allowed_extensions)) {
                $source_path = $source_folder . $file;
                $backup_path = $backup_folder . $file;
                $processed_path = $processed_folder . $file;
                
                // Sauvegarder l'original
                copy($source_path, $backup_path);
                
                // Obtenir la taille originale
                $original_size = getimagesize($source_path);
                $original_dimensions = $original_size ? $original_size[0] . 'x' . $original_size[1] : 'Erreur';
                
                // Traiter l'image
                $result = removeWhiteBorders($source_path, $processed_path);
                
                // Obtenir la nouvelle taille
                $new_size = getimagesize($processed_path);
                $new_dimensions = $new_size ? $new_size[0] . 'x' . $new_size[1] : 'Erreur';
                
                echo "<tr>";
                echo "<td>$file</td>";
                echo "<td>$original_dimensions</td>";
                echo "<td>$result</td>";
                echo "<td>$new_dimensions</td>";
                echo "</tr>";
                
                $processed_count++;
            }
        }
    }
    
    echo "</table>";
    echo "<p><strong>$processed_count images traitées</strong></p>";
    
    echo "<h2>Instructions:</h2>";
    echo "<ol>";
    echo "<li>Vérifiez les images dans le dossier '$processed_folder'</li>";
    echo "<li>Si elles sont correctes, remplacez les images du dossier '$source_folder' par celles du dossier '$processed_folder'</li>";
    echo "<li>Les originaux sont sauvegardés dans '$backup_folder'</li>";
    echo "</ol>";
    
} else {
    echo "<p style='color: red;'>Dossier '$source_folder' non trouvé!</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Retour au site</a> | <a href='test-scan-images.php'>Test scan images</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 { color: #8B4513; }
table { margin: 20px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f5f5f5; }
</style>

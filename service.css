/*
   Styles pour la section service - Sous le diaporama
   Couleurs du header : #F5F5DC (beige clair) pour le fond, #8B4513 (marron) pour le texte
*/

/* Section principale */
.service-section {
    background-color: #F5F5DC; /* Couleur du header */
    color: #8B4513; /* Couleur marron du texte */
    padding: 60px 0;
    font-family: 'Hiragino <PERSON>', 'Mei<PERSON>', 'Yu Gothic', sans-serif;
}

.service-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Titres de section */
.section-title {
    font-size: 2rem;
    font-weight: bold;
    color: #8B4513;
    text-align: center;
    margin-bottom: 40px;
    line-height: 1.4;
    padding: 0 20px;
}

/* 1. PARTIE PRÉSENTATION */
.presentation-section {
    margin-bottom: 80px;
    text-align: center;
}

.presentation-content {
    max-width: 800px;
    margin: 0 auto;
}

.presentation-text {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    color: #8B4513;
    text-align: justify;
}

/* 2. PARTIE RÉCOMPENSES */
.rewards-section {
    margin-bottom: 80px;
}

.rewards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Sous-partie gauche : Récompenses avec cadre doré */
.awards-subsection {
    display: flex;
    justify-content: center;
}

.golden-frame {
    /* Reproduction exacte du cadre doré de l'image */
    background: #FFFEF7; /* Fond blanc cassé comme sur l'image */
    border: 3px solid #DAA520; /* Bordure dorée principale */
    border-radius: 12px;
    padding: 25px;
    box-shadow:
        0 3px 6px rgba(218, 165, 32, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    position: relative;
    max-width: 380px;
    width: 100%;
}

/* Bordure intérieure fine comme sur l'image */
.golden-frame::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 1px solid #DAA520;
    border-radius: 6px;
    pointer-events: none;
}

.awards-title {
    font-size: 1.4rem;
    font-weight: bold;
    color: #8B4513; /* Couleur de la charte */
    text-align: center;
    margin-bottom: 20px;
    letter-spacing: 0.05em;
}

.awards-list {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
}

.awards-list li {
    /* Style moderne conforme à la charte */
    background: rgba(139, 69, 19, 0.05); /* Fond très léger marron */
    margin: 8px 0;
    padding: 12px 15px;
    border-radius: 8px;
    font-size: 0.95rem;
    color: #8B4513; /* Couleur de la charte */
    font-weight: 500;
    border-left: 4px solid #8B4513; /* Bordure marron de la charte */
    transition: all 0.3s ease;
}

.awards-list li:hover {
    background: rgba(139, 69, 19, 0.1);
    transform: translateX(3px);
}

/* Bouton moderne */
.modern-btn {
    background: linear-gradient(135deg, #8B4513, #A0522D);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.3);
    display: block;
    margin: 0 auto;
    font-family: inherit;
}

.modern-btn:hover {
    background: linear-gradient(135deg, #A0522D, #8B4513);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(139, 69, 19, 0.4);
}

.modern-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(139, 69, 19, 0.3);
}

/* Sous-partie droite : Actualités */
.news-subsection {
    background: rgba(255, 255, 255, 0.3);
    padding: 30px;
    border-radius: 15px;
    border: 2px solid rgba(139, 69, 19, 0.2);
}

.news-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #8B4513;
    text-align: center;
    margin-bottom: 25px;
}

.news-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.news-list li {
    margin: 12px 0;
    padding: 8px 0;
    border-bottom: 1px solid rgba(139, 69, 19, 0.2);
}

.news-list li:last-child {
    border-bottom: none;
}

.news-link {
    color: #8B4513;
    text-decoration: none;
    font-size: 0.95rem;
    line-height: 1.5;
    transition: all 0.3s ease;
    display: block;
    padding: 5px 10px;
    border-radius: 5px;
}

.news-link:hover {
    background: rgba(139, 69, 19, 0.1);
    color: #A0522D;
    transform: translateX(5px);
}

/* 3. PARTIE PROFIL DE L'ENTREPRISE */
.company-profile-section {
    margin-bottom: 80px;
}

.profile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 900px;
    margin: 0 auto;
}

.profile-item {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 10px;
    border-left: 4px solid #8B4513;
    align-items: start;
}

.profile-label {
    font-weight: bold;
    color: #8B4513;
    font-size: 1rem;
}

.profile-value {
    color: #8B4513;
    line-height: 1.6;
    font-size: 0.95rem;
}

.profile-link {
    color: #8B4513;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.profile-link:hover {
    color: #A0522D;
}

/* 3. PARTIE LOCALISATION - PLEINE LARGEUR */
.location-section-fullwidth {
    /* Technique sécurisée pour occuper toute la largeur de l'écran sans débordement */
    margin: 80px 0;
    width: 100vw;
    max-width: 100vw;
    position: relative;
    /* Centrer par rapport au viewport avec transform plus sûr */
    left: 50%;
    transform: translateX(-50%);
    /* Supprimer les propriétés qui causent le débordement */
    margin-left: 0;
    margin-right: 0;
    background-color: #F5F5DC;
    overflow: hidden;
    box-sizing: border-box;
}

.location-header {
    text-align: center;
    padding: 40px 20px 20px 20px;
    background-color: #F5F5DC;
}

.location-header .section-title {
    margin-bottom: 0;
}

.map-container-fullwidth {
    width: 100%;
    height: 500px;
    margin: 0;
    padding: 0;
    position: relative;
}

.map-container-fullwidth iframe {
    width: 100%;
    height: 100%;
    display: block;
    border: 0; /* Supprimer la bordure de l'iframe */
}

/* 4. PARTIE LOCALISATION - ANCIENNE VERSION (gardée pour compatibilité) */
.location-section {
    margin-bottom: 40px;
}

.map-container {
    max-width: 800px;
    margin: 0 auto;
}

.interactive-map {
    height: 400px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 16px rgba(139, 69, 19, 0.2);
    border: 2px solid rgba(139, 69, 19, 0.3);
}

.map-placeholder {
    height: 100%;
    background: linear-gradient(135deg, #F5F5DC, #FAEBD7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 40px;
}

.map-info h4 {
    font-size: 1.5rem;
    color: #8B4513;
    margin-bottom: 15px;
}

.map-info p {
    color: #8B4513;
    margin: 5px 0;
    font-size: 1.1rem;
}

.map-note {
    margin-top: 20px;
    padding: 10px 20px;
    background: rgba(139, 69, 19, 0.1);
    border-radius: 8px;
}

.map-note p {
    color: #8B4513;
    font-style: italic;
    margin: 0;
}

/* RESPONSIVE DESIGN - Mobile First */

/* Tablettes et écrans moyens */
@media (max-width: 768px) {
    .service-container {
        padding: 0 15px;
    }

    .section-title {
        font-size: 1.6rem;
        margin-bottom: 30px;
    }

    /* Récompenses : passage en colonne sur tablette */
    .rewards-container {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .golden-frame {
        max-width: none;
        margin: 0 auto;
    }

    /* Profil : ajustement des colonnes */
    .profile-item {
        grid-template-columns: 150px 1fr;
        gap: 15px;
        padding: 15px;
    }

    .profile-label {
        font-size: 0.9rem;
    }

    .profile-value {
        font-size: 0.9rem;
    }
}

/* Mobiles */
@media (max-width: 480px) {
    .service-section {
        padding: 40px 0;
    }

    .service-container {
        padding: 0 10px;
    }

    .section-title {
        font-size: 1.4rem;
        margin-bottom: 25px;
        padding: 0 10px;
    }

    .presentation-section,
    .rewards-section,
    .company-profile-section {
        margin-bottom: 50px;
    }

    .presentation-text {
        font-size: 1rem;
        text-align: left;
    }

    /* Cadre doré plus compact sur mobile */
    .golden-frame {
        padding: 20px;
        margin: 0;
    }

    .awards-title {
        font-size: 1.3rem;
    }

    .awards-list li {
        font-size: 0.9rem;
        padding: 8px 12px;
    }

    .modern-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    /* Actualités plus compactes */
    .news-subsection {
        padding: 20px;
    }

    .news-title {
        font-size: 1.3rem;
    }

    .news-link {
        font-size: 0.9rem;
    }

    /* Profil : passage en colonne sur mobile */
    .profile-item {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 15px;
        text-align: left;
    }

    .profile-label {
        font-weight: bold;
        border-bottom: 1px solid rgba(139, 69, 19, 0.3);
        padding-bottom: 5px;
        margin-bottom: 5px;
    }

    /* Carte pleine largeur responsive */
    .location-section-fullwidth {
        margin: 50px 0;
    }

    .location-header {
        padding: 30px 15px 15px 15px;
    }

    .map-container-fullwidth {
        height: 400px;
    }

    /* Carte plus petite sur mobile - ancienne version */
    .interactive-map {
        height: 300px;
    }

    .map-placeholder {
        padding: 20px;
    }

    .map-info h4 {
        font-size: 1.3rem;
    }

    .map-info p {
        font-size: 1rem;
    }
}

/* Très petits écrans */
@media (max-width: 320px) {
    .section-title {
        font-size: 1.2rem;
    }

    .golden-frame {
        padding: 15px;
    }

    .awards-title,
    .news-title {
        font-size: 1.2rem;
    }

    /* Carte pleine largeur sur très petits écrans */
    .location-section-fullwidth {
        margin: 40px 0;
    }

    .location-header {
        padding: 20px 10px 10px 10px;
    }

    .map-container-fullwidth {
        height: 300px;
    }

    /* Ancienne version */
    .interactive-map {
        height: 250px;
    }
}


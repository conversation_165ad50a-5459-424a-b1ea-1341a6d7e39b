<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Header Colors - <PERSON>saki</title>
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-white text-brown">
    <!-- Header -->
    <header>
        <div class="header-wrapper">
            <div class="header-container">
                <!-- Left Side: Logo and Contact Icons (Fixed Position) -->
                <div class="left-side">
                    <!-- Logo -->
                    <div id="logo" class="z-10 transition-all duration-500">
                        <a href="index.html" class="block logo-link">
                            <div class="logo">
                                <p class="logo-en">BABASAKI's woodworking factory</p>
                                <p class="logo-jp">馬場先木工所</p>
                            </div>
                        </a>
                    </div>

                    <!-- Contact Icons (Mobile) -->
                    <div class="contact-icons-mobile">
                        <div class="contact-icon">
                            <a href="tel:************" class="text-brown hover:text-green transition-colors">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                </svg>
                            </a>
                        </div>
                        <div class="contact-icon">
                            <a href="mailto:<EMAIL>" class="text-brown hover:text-green transition-colors">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Contact Info (Desktop) -->
                    <div class="contact-info-desktop">
                        <div class="contact-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="mr-2">
                                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                            </svg>
                            <span class="text-sm text-brown">TEL: ************</span>
                        </div>
                        <div class="contact-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="mr-2">
                                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                            </svg>
                            <span class="text-sm text-brown">FAX: ************</span>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Navigation (Fixed Position) -->
                <div class="right-side">
                    <!-- Hamburger Menu Button (Mobile) -->
                    <div class="burger-container">
                        <div id="menu-toggle" class="hamburger-menu">
                            <div class="icon-left"></div>
                            <div class="icon-right"></div>
                        </div>
                    </div>

                    <!-- Desktop Navigation -->
                    <nav class="desktop-nav" aria-label="Menu principal">
                        <a href="#about" class="nav-link">馬場先木工所とは</a>
                        <a href="performance.html" class="nav-link">制作実績・賞歴</a>
                        <a href="inquiry.html" class="nav-link">お問い合わせ</a>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main style="margin-top: 80px; padding: 20px;">
        <div style="text-align: center; padding: 40px;">
            <h1 style="font-size: 2rem; margin-bottom: 20px; color: #8B4513;">Test des couleurs du header</h1>
            <p style="font-size: 1.2rem; margin-bottom: 20px;">Ce fichier permet de tester les nouvelles couleurs du header.</p>

            <div style="margin: 40px 0;">
                <h2 style="font-size: 1.5rem; margin-bottom: 15px; color: #8B4513;">Couleurs utilisées :</h2>
                <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 20px;">
                    <div style="text-align: center;">
                        <div style="width: 100px; height: 50px; background-color: #EDE9E0; border: 1px solid #ccc; margin-bottom: 5px;"></div>
                        <small>#EDE9E0<br>Header Start</small>
                    </div>
                    <div style="text-align: center;">
                        <div style="width: 100px; height: 50px; background-color: #F0EFE8; border: 1px solid #ccc; margin-bottom: 5px;"></div>
                        <small>#F0EFE8<br>Header Light</small>
                    </div>
                    <div style="text-align: center;">
                        <div style="width: 100px; height: 50px; background-color: #F3F2EC; border: 1px solid #ccc; margin-bottom: 5px;"></div>
                        <small>#F3F2EC<br>Header Mid</small>
                    </div>
                    <div style="text-align: center;">
                        <div style="width: 100px; height: 50px; background-color: #F5F5DC; border: 1px solid #ccc; margin-bottom: 5px;"></div>
                        <small>#F5F5DC<br>Beige Standard</small>
                    </div>
                </div>
            </div>

            <p style="margin-top: 30px; font-style: italic;">
                Le dégradé du header devrait maintenant s'harmoniser parfaitement avec l'image back_img.jpg
            </p>

            <div style="margin-top: 40px; padding: 20px; background: #f9f9f9; border-radius: 8px;">
                <h3 style="color: #8B4513; margin-bottom: 15px;">Logique corrigée :</h3>
                <ul style="text-align: left; max-width: 700px; margin: 0 auto;">
                    <li><strong>Mobile/Tablette (< 774px) :</strong> Couleur unie #F8F7F2 (image dans mobile-menu-content)</li>
                    <li><strong>Desktop (≥ 774px) :</strong> Dégradé + image back_img.jpg dans le header</li>
                    <li><strong>Point de rupture :</strong> 774px (quand le burger disparaît)</li>
                    <li><strong>Technique :</strong> Media query précise pour éviter les conflits</li>
                </ul>

                <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <strong>Note :</strong> Redimensionnez la fenêtre pour voir le changement à 774px !
                </div>
            </div>
        </div>
    </main>

    <script src="js/main.js"></script>
</body>
</html>

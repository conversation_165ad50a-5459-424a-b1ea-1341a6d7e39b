<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile - Suppression Bandes Blanches</title>
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="diaporama.css">
    <style>
        /* Styles de débogage pour visualiser les problèmes */
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
        }
        
        .debug-red-border {
            border: 2px solid red !important;
        }
        
        .debug-green-border {
            border: 2px solid green !important;
        }
        
        /* Test avec fond coloré pour voir les espaces vides */
        .slideshow-container {
            background: #ff0000 !important; /* Rouge pour voir les espaces non couverts */
        }
        
        .slide {
            background: #00ff00 !important; /* Vert pour voir les slides */
        }
    </style>
</head>
<body class="bg-white text-brown">
    <!-- Info de débogage -->
    <div class="debug-info">
        <strong>Test Mobile</strong><br>
        Écran: <span id="screen-size"></span><br>
        Container: <span id="container-size"></span><br>
        Image: <span id="image-size"></span><br>
        <button onclick="toggleDebugBorders()">Toggle Borders</button>
    </div>

    <!-- Header simplifié -->
    <header class="bg-light-beige" style="height: 80px; position: fixed; top: 0; left: 0; right: 0; z-index: 100; background: #F5F5DC;">
        <div style="display: flex; align-items: center; justify-content: center; height: 100%;">
            <h1 style="color: #8B4513; font-size: 1.2rem;">Test Mobile - Suppression Bandes Blanches</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Container principal du diaporama -->
        <div class="slideshow-container with-header">
            <!-- Test avec quelques images -->
            <div class="slide active">
                <img src="diaporama/1.jpg" alt="Test 1" loading="lazy">
            </div>
            <div class="slide">
                <img src="diaporama/2.jpg" alt="Test 2" loading="lazy">
            </div>
            <div class="slide">
                <img src="diaporama/3.jpg" alt="Test 3" loading="lazy">
            </div>

            <!-- Contrôles de navigation -->
            <button class="nav-btn prev-btn" aria-label="Image précédente">
                <span>&#8249;</span>
            </button>
            <button class="nav-btn next-btn" aria-label="Image suivante">
                <span>&#8250;</span>
            </button>

            <!-- Indicateurs -->
            <div class="indicators">
                <button class="indicator active" data-slide="0"></button>
                <button class="indicator" data-slide="1"></button>
                <button class="indicator" data-slide="2"></button>
            </div>

            <!-- Compteur de slides -->
            <div class="slide-counter">
                <span class="current-slide">1</span> / <span class="total-slides">3</span>
            </div>
        </div>
    </main>

    <script src="js/main.js"></script>
    <script src="diaporama.js"></script>
    
    <script>
        // Script de débogage pour mobile
        function updateDebugInfo() {
            const screenSize = document.getElementById('screen-size');
            const containerSize = document.getElementById('container-size');
            const imageSize = document.getElementById('image-size');
            
            const container = document.querySelector('.slideshow-container');
            const activeImg = document.querySelector('.slide.active img');
            
            if (screenSize) {
                screenSize.textContent = `${window.innerWidth}x${window.innerHeight}`;
            }
            
            if (container && containerSize) {
                containerSize.textContent = `${container.offsetWidth}x${container.offsetHeight}`;
            }
            
            if (activeImg && imageSize) {
                imageSize.textContent = `${activeImg.offsetWidth}x${activeImg.offsetHeight}`;
            }
        }
        
        function toggleDebugBorders() {
            const container = document.querySelector('.slideshow-container');
            const slides = document.querySelectorAll('.slide');
            const images = document.querySelectorAll('.slide img');
            
            container.classList.toggle('debug-red-border');
            slides.forEach(slide => slide.classList.toggle('debug-green-border'));
            images.forEach(img => img.classList.toggle('debug-red-border'));
        }
        
        // Mettre à jour les infos de débogage
        window.addEventListener('load', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        
        // Mettre à jour quand l'image change
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(updateDebugInfo, 1000);
            setInterval(updateDebugInfo, 2000);
        });
        
        // Log des dimensions pour le débogage
        console.log('=== TEST MOBILE - SUPPRESSION BANDES BLANCHES ===');
        console.log('Écran:', window.innerWidth + 'x' + window.innerHeight);
        console.log('User Agent:', navigator.userAgent);
        console.log('Device Pixel Ratio:', window.devicePixelRatio);
    </script>
</body>
</html>

<?php
// Script de test pour vérifier la détection automatique des images
echo "<h1>Test de détection automatique des images</h1>";

// Scan automatique du dossier diaporama
$diaporama_folder = 'diaporama/';
$diaporama_images = [];

// Extensions d'images supportées
$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];

echo "<h2>Configuration :</h2>";
echo "<p><strong>Dossier scanné :</strong> " . $diaporama_folder . "</p>";
echo "<p><strong>Extensions supportées :</strong> " . implode(', ', $allowed_extensions) . "</p>";

// Vérifier si le dossier existe
if (is_dir($diaporama_folder)) {
    echo "<p style='color: green;'><strong>✓ Dossier trouvé</strong></p>";
    
    // Scanner le dossier
    $files = scandir($diaporama_folder);
    
    echo "<h2>Fichiers trouvés dans le dossier :</h2>";
    echo "<ul>";
    
    foreach ($files as $file) {
        // Ignorer les dossiers . et ..
        if ($file != '.' && $file != '..') {
            echo "<li>";
            
            // Récupérer l'extension du fichier
            $file_extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            
            // Vérifier si c'est une image supportée
            if (in_array($file_extension, $allowed_extensions)) {
                $diaporama_images[] = $diaporama_folder . $file;
                echo "<span style='color: green;'>✓ " . $file . " (Extension: " . $file_extension . ") - <strong>INCLUS</strong></span>";
            } else {
                echo "<span style='color: orange;'>⚠ " . $file . " (Extension: " . $file_extension . ") - <strong>IGNORÉ</strong></span>";
            }
            
            echo "</li>";
        }
    }
    echo "</ul>";
    
    // Trier les images par nom pour un ordre cohérent
    sort($diaporama_images);
    
    // Résultats finaux
    $total_images = count($diaporama_images);
    
    echo "<h2>Résultats :</h2>";
    echo "<p><strong>Nombre total d'images détectées :</strong> <span style='font-size: 1.5em; color: blue;'>" . $total_images . "</span></p>";
    
    if ($total_images > 0) {
        echo "<h3>Images qui seront utilisées dans le diaporama :</h3>";
        echo "<ol>";
        foreach ($diaporama_images as $index => $image) {
            echo "<li>" . $image . "</li>";
        }
        echo "</ol>";
        
        echo "<p style='color: green; font-weight: bold;'>✓ Le diaporama sera généré avec " . $total_images . " images</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ Aucune image valide trouvée - Le diaporama affichera un message d'erreur</p>";
    }
    
} else {
    echo "<p style='color: red;'><strong>✗ Dossier non trouvé !</strong></p>";
    echo "<p>Veuillez créer le dossier 'diaporama/' et y ajouter des images.</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Retour au site principal</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #8B4513;
}
ul, ol {
    margin: 10px 0;
}
li {
    margin: 5px 0;
}
</style>
